# optikarburant/settings.py - Main Django settings for OptiKarburant System

import os
from pathlib import Path
from django.utils.translation import gettext_lazy as _

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-optikarburant-change-this-in-production'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0']

# Application definition - OptiKarburant Core Apps
DJANGO_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.gis',  # GeoDjango for spatial operations
]

THIRD_PARTY_APPS = [
    'rest_framework',     # Django REST Framework for API
    'corsheaders',        # CORS headers for frontend API access
    'celery',            # Background task processing
    'django_extensions', # Useful Django extensions
]

LOCAL_APPS = [
    'logistics',         # Core logistics models and operations
    'optimization',      # Route optimization engine using OR-Tools
    'dashboard',         # Web dashboard interface
    'api',              # REST API endpoints
    'mobile',           # Mobile driver interface
    'reports',          # Analytics and reporting
]

INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.locale.LocaleMiddleware',  # i18n support
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'optikarburant.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.i18n',
            ],
        },
    },
]

WSGI_APPLICATION = 'optikarburant.wsgi.application'

# Database - PostgreSQL with PostGIS for spatial operations
DATABASES = {
    'default': {
        'ENGINE': 'django.contrib.gis.db.backends.postgis',
        'NAME': os.getenv('DB_NAME', 'optikarburant'),
        'USER': os.getenv('DB_USER', 'postgres'),
        'PASSWORD': os.getenv('DB_PASSWORD', 'postgres'),
        'HOST': os.getenv('DB_HOST', 'localhost'),
        'PORT': os.getenv('DB_PORT', '5432'),
    }
}

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization (English/Albanian support)
LANGUAGE_CODE = 'en'  # Default language
TIME_ZONE = 'Europe/Tirane'  # Albanian timezone
USE_I18N = True
USE_L10N = True
USE_TZ = True

# Available languages
LANGUAGES = [
    ('en', _('English')),
    ('sq', _('Albanian')),
]

# Locale paths for translations
LOCALE_PATHS = [
    BASE_DIR / 'locale',
]

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# GDAL Configuration for GeoDjango
import sys
if sys.platform == 'win32':
    # Windows GDAL configuration
    GDAL_LIBRARY_PATH = r'C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\osgeo\gdal.dll'
    GEOS_LIBRARY_PATH = r'C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\osgeo\geos_c.dll'

# Django REST Framework Configuration
REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 50,
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.TokenAuthentication',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
}

# Celery Configuration for Background Tasks
CELERY_BROKER_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
CELERY_RESULT_BACKEND = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE

# CORS Configuration
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]

# Logging Configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'optikarburant.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'logistics': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'optimization': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}

# OptiKarburant System Configuration
OPTIKARBURANT_SETTINGS = {
    # Central Depot Configuration (Tirana coordinates as default)
    'DEPOT_CENTRAL': {
        'name': 'Depo Qendrore',
        'latitude': 41.3275,
        'longitude': 19.8189,
        'address': 'Tiranë, Shqipëri'
    },
    
    # Optimization Engine Settings
    'OPTIMIZATION': {
        'MAX_ROUTE_DURATION_HOURS': 10,
        'MAX_WORKING_HOURS_PER_DAY': 8,
        'MANDATORY_BREAK_AFTER_HOURS': 4.5,
        'BREAK_DURATION_MINUTES': 45,
        'DEFAULT_TRUCK_SPEED_KMH': 50,
        'OPTIMIZATION_TIMEOUT_SECONDS': 300,
        'MAX_STOPS_PER_ROUTE': 15,
    },
    
    # Business Rules
    'BUSINESS_RULES': {
        'HEEL_STOCK_DEFAULT_LITERS': 50,
        'SAFETY_STOCK_PERCENTAGE': 10,
        'MAX_TANK_FILL_PERCENTAGE': 95,
        'VARIANCE_THRESHOLD_PERCENTAGE': 5,
        'EMERGENCY_ORDER_DAYS': 1,
        'DEFAULT_SERVICE_TIME_MINUTES': 45,
    },
    
    # External APIs and Services
    'EXTERNAL_SERVICES': {
        'OSRM_SERVER_URL': os.getenv('OSRM_URL', 'http://localhost:5000'),
        'NOMINATIM_SERVER_URL': os.getenv('NOMINATIM_URL', 'http://localhost:8080'),
        'GOOGLE_MAPS_API_KEY': os.getenv('GOOGLE_MAPS_API_KEY', ''),
        'USE_EXTERNAL_ROUTING': os.getenv('USE_EXTERNAL_ROUTING', 'false').lower() == 'true',
    },
    
    # Map Configuration
    'MAP_SETTINGS': {
        'DEFAULT_ZOOM': 10,
        'CENTER_LAT': 41.3275,
        'CENTER_LNG': 19.8189,
        'TILE_SERVER': 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
        'ATTRIBUTION': '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
    }
}

# Email Configuration (for notifications)
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.getenv('EMAIL_HOST', 'localhost')
EMAIL_PORT = int(os.getenv('EMAIL_PORT', '587'))
EMAIL_USE_TLS = os.getenv('EMAIL_USE_TLS', 'true').lower() == 'true'
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', '<EMAIL>')

# Security Settings for Production
if not DEBUG:
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_SECONDS = 31536000
    SECURE_REDIRECT_EXEMPT = []
    SECURE_SSL_REDIRECT = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True