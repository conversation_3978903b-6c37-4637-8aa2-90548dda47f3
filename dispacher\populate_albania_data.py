#!/usr/bin/env python
"""
Standalone script to populate OptiKarburant database with Albanian sample data
This script can be run independently of Django management commands
"""

import os
import sys
import django
from datetime import datetime, time, timedelta
import random

# Setup Django environment with simple settings (no spatial features)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'optikarburant.settings_simple')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    django.setup()
    from django.contrib.gis.geos import Point
    from django.utils import timezone
    from logistics.models import (
        Produkt, DepoQendrore, Stacion, Depozite, <PERSON>er, 
        <PERSON><PERSON><PERSON>, Particion, Porosi
    )
    print("✓ Django setup successful")
except Exception as e:
    print(f"✗ Django setup failed: {e}")
    print("This might be due to missing GDAL libraries.")
    print("Please install PostGIS and GDAL, or modify settings to use SQLite.")
    sys.exit(1)

def clear_existing_data():
    """Clear all existing data"""
    print("Clearing existing data...")
    Porosi.objects.all().delete()
    Particion.objects.all().delete()
    Kamion.objects.all().delete()
    Shofer.objects.all().delete()
    Depozite.objects.all().delete()
    Stacion.objects.all().delete()
    DepoQendrore.objects.all().delete()
    Produkt.objects.all().delete()
    print("✓ Existing data cleared")

def create_products():
    """Create Albanian fuel products"""
    print("Creating fuel products...")
    products_data = [
        {'emri': 'Naftë D2', 'densiteti': 0.85, 'ngjyra_kodi': '#2c3e50'},
        {'emri': 'Benzinë 95', 'densiteti': 0.75, 'ngjyra_kodi': '#e74c3c'},
        {'emri': 'Benzinë 100', 'densiteti': 0.75, 'ngjyra_kodi': '#c0392b'},
        {'emri': 'Gaz i Lëngshëm (LPG)', 'densiteti': 0.51, 'ngjyra_kodi': '#3498db'},
        {'emri': 'Naftë Ngrohje', 'densiteti': 0.87, 'ngjyra_kodi': '#8e44ad'},
    ]
    
    products = []
    for data in products_data:
        product, created = Produkt.objects.get_or_create(
            emri=data['emri'],
            defaults=data
        )
        products.append(product)
        if created:
            print(f"  ✓ Created product: {product.emri}")
    
    # Set product compatibility
    nafte = products[0]  # Naftë D2
    benzine_95 = products[1]  # Benzinë 95
    benzine_100 = products[2]  # Benzinë 100
    
    # Benzines are compatible with each other
    benzine_95.produkte_kompatible.add(benzine_100)
    benzine_100.produkte_kompatible.add(benzine_95)
    
    print(f"✓ Created {len(products)} products")
    return products

def create_central_depot():
    """Create central depot in Tirana"""
    print("Creating central depot...")
    depot, created = DepoQendrore.objects.get_or_create(
        emri='Depo Qendrore Tiranë',
        defaults={
            'vendndodhja': Point(19.8189, 41.3275),  # Tirana coordinates
            'adresa': 'Rruga Industriale, Tiranë, Shqipëri',
            'kapaciteti_ngarkimi': 8,
            'koha_mesatare_ngarkimi': 90,
            'orar_punes_nga': time(6, 0),
            'orar_punes_deri': time(22, 0),
        }
    )
    if created:
        print(f"  ✓ Created depot: {depot.emri}")
    print("✓ Central depot ready")
    return depot

def create_stations():
    """Create comprehensive Albanian fuel stations"""
    print("Creating fuel stations...")
    stations_data = [
        # Tirana area - Major distribution hub
        {'emri': 'Stacioni Qender Tiranë', 'kodi': 'TIR001', 'lat': 41.3275, 'lng': 19.8189, 'city': 'Tiranë', 'type': 'major'},
        {'emri': 'Stacioni Kombinat', 'kodi': 'TIR002', 'lat': 41.2911, 'lng': 19.8607, 'city': 'Tiranë', 'type': 'standard'},
        {'emri': 'Stacioni Don Bosko', 'kodi': 'TIR003', 'lat': 41.3151, 'lng': 19.8331, 'city': 'Tiranë', 'type': 'standard'},
        {'emri': 'Stacioni Kashar', 'kodi': 'TIR004', 'lat': 41.3847, 'lng': 19.7736, 'city': 'Tiranë', 'type': 'highway'},
        {'emri': 'Stacioni Kamëz', 'kodi': 'TIR005', 'lat': 41.3814, 'lng': 19.7631, 'city': 'Tiranë', 'type': 'standard'},
        
        # Durrës - Port city, high volume
        {'emri': 'Stacioni Durrës Port', 'kodi': 'DUR001', 'lat': 41.3147, 'lng': 19.4444, 'city': 'Durrës', 'type': 'major'},
        {'emri': 'Stacioni Durrës Qender', 'kodi': 'DUR002', 'lat': 41.3236, 'lng': 19.4581, 'city': 'Durrës', 'type': 'standard'},
        {'emri': 'Stacioni Durrës Plazh', 'kodi': 'DUR003', 'lat': 41.3200, 'lng': 19.4300, 'city': 'Durrës', 'type': 'seasonal'},
        
        # Other major cities
        {'emri': 'Stacioni Shkodër Qender', 'kodi': 'SHK001', 'lat': 42.0683, 'lng': 19.5122, 'city': 'Shkodër', 'type': 'major'},
        {'emri': 'Stacioni Elbasan Qender', 'kodi': 'ELB001', 'lat': 41.1125, 'lng': 20.0822, 'city': 'Elbasan', 'type': 'major'},
        {'emri': 'Stacioni Vlorë Port', 'kodi': 'VLO001', 'lat': 40.4686, 'lng': 19.4889, 'city': 'Vlorë', 'type': 'major'},
        {'emri': 'Stacioni Korçë Qender', 'kodi': 'KOR001', 'lat': 40.6186, 'lng': 20.7719, 'city': 'Korçë', 'type': 'major'},
        {'emri': 'Stacioni Fier Qender', 'kodi': 'FIE001', 'lat': 40.7239, 'lng': 19.5556, 'city': 'Fier', 'type': 'major'},
        {'emri': 'Stacioni Fier Industrial', 'kodi': 'FIE002', 'lat': 40.7300, 'lng': 19.5600, 'city': 'Fier', 'type': 'industrial'},
        
        # Additional stations
        {'emri': 'Stacioni Berat', 'kodi': 'BER001', 'lat': 40.7058, 'lng': 19.9522, 'city': 'Berat', 'type': 'standard'},
        {'emri': 'Stacioni Gjirokastër', 'kodi': 'GJI001', 'lat': 40.0758, 'lng': 20.1389, 'city': 'Gjirokastër', 'type': 'standard'},
        {'emri': 'Stacioni Lushnjë', 'kodi': 'LUS001', 'lat': 40.9419, 'lng': 19.7050, 'city': 'Lushnjë', 'type': 'standard'},
        {'emri': 'Stacioni Pogradec', 'kodi': 'POG001', 'lat': 40.9022, 'lng': 20.6528, 'city': 'Pogradec', 'type': 'seasonal'},
        {'emri': 'Stacioni Kukës', 'kodi': 'KUK001', 'lat': 42.0772, 'lng': 20.4214, 'city': 'Kukës', 'type': 'standard'},
        {'emri': 'Stacioni Lezhë', 'kodi': 'LEZ001', 'lat': 41.7836, 'lng': 19.6439, 'city': 'Lezhë', 'type': 'standard'},
    ]
    
    stations = []
    for data in stations_data:
        # Set operating parameters based on station type
        if data['type'] == 'major':
            start_time, end_time = time(5, 0), time(20, 0)
            max_trucks, unload_time = 2, 60
            requires_pump, requires_meter = True, True
        elif data['type'] == 'highway':
            start_time, end_time = time(0, 0), time(23, 59)
            max_trucks, unload_time = 3, 30
            requires_pump, requires_meter = False, True
        elif data['type'] == 'industrial':
            start_time, end_time = time(6, 0), time(18, 0)
            max_trucks, unload_time = 2, 90
            requires_pump, requires_meter = True, True
        elif data['type'] == 'seasonal':
            start_time, end_time = time(7, 0), time(19, 0)
            max_trucks, unload_time = 1, 45
            requires_pump, requires_meter = False, True
        else:  # standard
            start_time, end_time = time(6, 0), time(18, 0)
            max_trucks, unload_time = 1, 45
            requires_pump, requires_meter = False, True
        
        station, created = Stacion.objects.get_or_create(
            kodi=data['kodi'],
            defaults={
                'emri': data['emri'],
                'vendndodhja': Point(data['lng'], data['lat']),
                'adresa': f"{data['emri']}, {data['city']}, Shqipëri",
                'orar_pranimi_nga': start_time,
                'orar_pranimi_deri': end_time,
                'kerkon_pompe': requires_pump,
                'kerkon_kontaliter': requires_meter,
                'max_kamione_njekohesisht': max_trucks,
                'koha_mesatare_shkarkimi': unload_time,
                'max_pesha_kamioni_ton': 40.0 if data['type'] != 'seasonal' else 25.0,
                'max_gjatesia_kamioni_m': 16.5 if data['type'] != 'seasonal' else 12.0,
                'menaxher_emri': f"Menaxher {data['city']}",
                'telefoni': f"+35569{random.randint(1000000, 9999999)}",
            }
        )
        stations.append(station)
        if created:
            print(f"  ✓ Created station: {station.emri}")
    
    print(f"✓ Created {len(stations)} stations")
    return stations

def main():
    """Main function to populate the database"""
    print("🇦🇱 OptiKarburant Albania - Sample Data Population")
    print("=" * 50)
    
    try:
        # Clear existing data
        clear_existing_data()
        
        # Create products
        products = create_products()
        
        # Create central depot
        depot = create_central_depot()
        
        # Create stations
        stations = create_stations()
        
        print("\n" + "=" * 50)
        print("✅ Sample data population completed successfully!")
        print(f"📊 Summary:")
        print(f"   • Products: {len(products)}")
        print(f"   • Central Depot: 1")
        print(f"   • Stations: {len(stations)}")
        print("\n🚀 You can now run the Django server and explore the data!")
        
    except Exception as e:
        print(f"\n❌ Error during data population: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
